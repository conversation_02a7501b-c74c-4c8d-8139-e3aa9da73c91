import { auth } from '@/src/lib/auth';
import { NextResponse } from 'next/server';

export default auth((req) => {
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;
  const isAdmin = req.auth?.user?.role === 'admin';
  const userEmail = req.auth?.user?.email;

  console.log(`🛡️ Middleware check: ${nextUrl.pathname} | User: ${userEmail || 'none'} | Role: ${req.auth?.user?.role || 'none'} | Logged in: ${isLoggedIn}`);

  // Protect admin routes
  if (nextUrl.pathname.startsWith('/admin')) {
    console.log(`🔒 Admin route access attempt: ${nextUrl.pathname}`);

    if (!isLoggedIn) {
      console.log('❌ Access denied: Not logged in, redirecting to login');
      return NextResponse.redirect(new URL('/auth/login', nextUrl));
    }

    if (!isAdmin) {
      console.log('❌ Access denied: Not admin role, redirecting to home');
      return NextResponse.redirect(new URL('/', nextUrl));
    }

    console.log('✅ Admin access granted');
  }

  // Redirect to admin if already logged in and trying to access login
  if (nextUrl.pathname.startsWith('/auth/login') && isLoggedIn && isAdmin) {
    console.log('🔄 Redirecting logged-in admin from login to admin dashboard');
    return NextResponse.redirect(new URL('/admin', nextUrl));
  }

  return NextResponse.next();
});

export const config = {
  matcher: [
    '/admin/:path*',
    '/auth/login',
    '/api/admin/:path*'
  ],
};