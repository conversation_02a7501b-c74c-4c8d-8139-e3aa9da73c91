import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/src/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    console.log('Admin auth check - Session:', session?.user?.email, 'Role:', session?.user?.role);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated', authenticated: false, isAdmin: false },
        { status: 401 }
      );
    }
    
    if (session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized', authenticated: true, isAdmin: false },
        { status: 403 }
      );
    }
    
    return NextResponse.json({
      authenticated: true,
      isAdmin: true,
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role,
      }
    });
    
  } catch (error) {
    console.error('Admin auth check error:', error);
    return NextResponse.json(
      { error: 'Authentication check failed', authenticated: false, isAdmin: false },
      { status: 500 }
    );
  }
}
