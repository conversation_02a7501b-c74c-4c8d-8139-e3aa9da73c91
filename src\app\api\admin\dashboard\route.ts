import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/src/lib/auth';
import dbConnect from '@/src/lib/database';
import BlogPost from '@/src/lib/models/BlogPost';
import User from '@/src/lib/models/User';
import ActivityLog from '@/src/lib/models/ActivityLog';
import PdfOperation from '@/src/lib/models/PdfOperation';

// Mock data for demo purposes
const MOCK_DASHBOARD_DATA = {
  totalPosts: 5,
  totalUsers: 1,
  totalViews: 1168,
  pdfOperations: 15,
  recentActivity: [
    { id: '1', user: 'Admin User', action: 'login', resource: 'auth', timestamp: new Date().toISOString() },
    { id: '2', user: 'Demo User', action: 'merge', resource: 'pdf', timestamp: new Date(Date.now() - 3600000).toISOString() },
    { id: '3', user: 'Test User', action: 'view', resource: 'blog', timestamp: new Date(Date.now() - 7200000).toISOString() },
  ],
  popularPosts: [
    { title: 'How to Merge PDF Files Efficiently', views: 342, slug: 'merge-pdf-files-efficiently' },
    { title: 'Best Practices for PDF Compression', views: 289, slug: 'pdf-compression-best-practices' },
    { title: 'Converting Images to PDF: Complete Guide', views: 156, slug: 'converting-images-to-pdf-guide' },
  ],
  userRegistrations: [
    { name: 'Jan', value: 12 },
    { name: 'Feb', value: 19 },
    { name: 'Mar', value: 15 },
    { name: 'Apr', value: 28 },
    { name: 'May', value: 22 },
    { name: 'Jun', value: 35 },
  ],
  pdfOperationStats: [
    { name: 'Merge', value: 35 },
    { name: 'Split', value: 18 },
    { name: 'Compress', value: 22 },
    { name: 'Convert', value: 12 },
    { name: 'Protect', value: 8 },
  ],
};

export async function GET(request: NextRequest) {
  console.log('Admin dashboard API called');

  // Check authentication and authorization using NextAuth
  const session = await auth();

  if (!session || !session.user) {
    console.log('No session found');
    return NextResponse.json(
      { error: 'Not authenticated' },
      { status: 401 }
    );
  }

  if (session.user.role !== 'admin') {
    console.log('User not admin:', session.user.role);
    return NextResponse.json(
      { error: 'Not authorized' },
      { status: 403 }
    );
  }

  console.log('Admin access granted for:', session.user.email);

  try {
    const dbConnection = await dbConnect();

    if (!dbConnection) {
      console.log('No database connection, returning mock data');
      return NextResponse.json(MOCK_DASHBOARD_DATA);
    }

    console.log('Fetching dashboard statistics...');

    try {
      // Fetch basic counts
      const [totalPosts, totalUsers, totalViews, pdfOperations] = await Promise.all([
        BlogPost.countDocuments({ status: 'published' }),
        User.countDocuments({ isActive: true }),
        BlogPost.aggregate([{ $group: { _id: null, total: { $sum: '$views' } } }]),
        PdfOperation.countDocuments(),
      ]);

      // Get recent activity
      const recentActivity = await ActivityLog.find()
        .populate('user', 'name email')
        .sort({ createdAt: -1 })
        .limit(10)
        .lean();

      // Get popular posts
      const popularPosts = await BlogPost.find({ status: 'published' })
        .select('title slug views')
        .sort({ views: -1 })
        .limit(5)
        .lean();

      // Get user registrations by month (last 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const userRegistrations = await User.aggregate([
        {
          $match: {
            createdAt: { $gte: sixMonthsAgo }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            count: { $sum: 1 }
          }
        },
        {
          $sort: { '_id.year': 1, '_id.month': 1 }
        }
      ]);

      // Get PDF operation statistics
      const pdfOperationStats = await PdfOperation.aggregate([
        {
          $group: {
            _id: '$operation',
            count: { $sum: 1 }
          }
        }
      ]);

      // Format the data
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const formattedUserRegistrations = userRegistrations.map(item => ({
        name: monthNames[item._id.month - 1],
        value: item.count
      }));

      const formattedPdfStats = pdfOperationStats.map(item => ({
        name: item._id.charAt(0).toUpperCase() + item._id.slice(1),
        value: item.count
      }));

      const formattedActivity = recentActivity.map(activity => ({
        id: (activity._id as any).toString(),
        user: (activity.user as any)?.name || 'Unknown User',
        action: activity.action,
        resource: activity.resource,
        timestamp: activity.createdAt
      }));

      const formattedPopularPosts = popularPosts.map(post => ({
        title: post.title,
        views: post.views,
        slug: post.slug
      }));

      const dashboardData = {
        totalPosts,
        totalUsers,
        totalViews: totalViews[0]?.total || 0,
        pdfOperations,
        recentActivity: formattedActivity,
        popularPosts: formattedPopularPosts,
        userRegistrations: formattedUserRegistrations.length > 0 ? formattedUserRegistrations : MOCK_DASHBOARD_DATA.userRegistrations,
        pdfOperationStats: formattedPdfStats.length > 0 ? formattedPdfStats : MOCK_DASHBOARD_DATA.pdfOperationStats,
      };

      console.log('Dashboard data prepared successfully');
      return NextResponse.json(dashboardData);

    } catch (dbError) {
      console.error('Database query failed, returning mock data:', dbError);
      return NextResponse.json(MOCK_DASHBOARD_DATA);
    }

  } catch (error) {
    console.error('Dashboard API error:', error);

    // Return mock data as fallback
    return NextResponse.json(MOCK_DASHBOARD_DATA);
  }
}