import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import dbConnect from '@/src/lib/database';
import User from '@/src/lib/models/User';
import { signJWT } from '@/src/lib/auth-helpers';
import ActivityLog from '@/src/lib/models/ActivityLog';

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

// Removed demo admin - using database only

export async function POST(request: NextRequest) {
  console.log('Login API called');

  try {
    const dbConnection = await dbConnect();

    const body = await request.json();
    const { email, password } = loginSchema.parse(body);

    console.log('Login attempt for email:', email);

    if (!dbConnection) {
      console.log('Database connection failed');
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Find user in database
    const user = await User.findOne({ email, isActive: true });

    if (!user) {
      console.log('User not found:', email);
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Verify password
    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      console.log('Invalid password for user:', email);
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Update last login if database is available
    if (dbConnection && user._id !== 'demo-admin-id') {
      await User.findByIdAndUpdate(user._id, { lastLogin: new Date() });
    }

    // Log activity if database is available
    if (dbConnection) {
      try {
        await ActivityLog.create({
          user: user._id,
          action: 'login',
          resource: 'auth',
          ipAddress: request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'unknown',
          userAgent: request.headers.get('user-agent'),
        });
      } catch (logError) {
        console.error('Failed to log activity:', logError);
        // Don't fail login if logging fails
      }
    }

    // Generate JWT
    const token = signJWT({
      userId: user._id.toString(),
      email: user.email,
      role: user.role,
      name: user.name,
    });

    console.log('Login successful for user:', email);

    return NextResponse.json({
      user: {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        role: user.role,
        avatar: user.avatar || '',
      },
      token,
    });

  } catch (error) {
    console.error('Login error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}