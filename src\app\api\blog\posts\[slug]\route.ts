import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/src/lib/database';
import BlogPost from '@/src/lib/models/BlogPost';

// Mock post data for demo
const MOCK_POST_DATA = {
  'merge-pdf-files-efficiently': {
    _id: '1',
    title: 'How to Merge PDF Files Efficiently',
    content: `# How to Merge PDF Files Efficiently

Merging PDF files is one of the most common document management tasks. Whether you're combining reports, consolidating invoices, or creating comprehensive documents, our PDF merge tool makes the process simple and fast.

## Why Merge PDFs?

- **Organization**: Keep related documents together
- **Sharing**: Send one file instead of multiple attachments  
- **Printing**: Print everything in one go
- **Storage**: Reduce file clutter

## Best Practices

1. **Order matters**: Arrange files in the sequence you want them to appear
2. **File naming**: Use descriptive names for easy identification
3. **Size consideration**: Large files may take longer to process
4. **Quality check**: Review the merged document before sharing

## Step-by-Step Guide

1. Navigate to our PDF Merge tool
2. Click "Choose Files" or drag and drop your PDFs
3. Arrange the files in your desired order
4. Click "Merge PDFs"
5. Download your combined document

Our tool processes files securely and automatically deletes them after 24 hours for your privacy.`,
    excerpt: 'Learn how to efficiently merge multiple PDF files into a single document with our comprehensive guide and best practices.',
    author: { name: 'Admin User', email: '<EMAIL>' },
    category: 'tutorials',
    tags: ['pdf', 'merge', 'tools', 'guide'],
    views: 342,
    publishedAt: '2024-01-15T00:00:00.000Z',
  },
  'pdf-compression-best-practices': {
    _id: '2',
    title: 'Best Practices for PDF Compression',
    content: `# Best Practices for PDF Compression

PDF compression is essential for reducing file sizes while maintaining document quality. This guide covers everything you need to know about optimizing your PDFs.

## Understanding PDF Compression

PDF compression works by:
- Removing redundant data
- Optimizing images
- Streamlining fonts
- Compressing text and graphics

## When to Compress PDFs

- **Email attachments**: Stay under size limits
- **Web uploads**: Faster loading times
- **Storage**: Save disk space
- **Mobile devices**: Easier handling on phones/tablets

## Compression Levels

### Light Compression (90-95% quality)
- Minimal file size reduction
- Best for final documents
- Maintains high image quality

### Medium Compression (75-85% quality)
- Balanced approach
- Good for most use cases
- Reasonable quality retention

### Heavy Compression (50-70% quality)
- Maximum size reduction
- Use for drafts or internal documents
- May affect image clarity

## Tips for Better Results

1. **Start with high-quality originals**
2. **Choose appropriate compression levels**
3. **Test different settings**
4. **Consider your use case**
5. **Always keep original backups**`,
    excerpt: 'Discover the best practices for compressing PDF files to reduce size while maintaining quality for various use cases.',
    author: { name: 'Admin User', email: '<EMAIL>' },
    category: 'optimization',
    tags: ['pdf', 'compression', 'optimization', 'quality'],
    views: 289,
    publishedAt: '2024-01-20T00:00:00.000Z',
  }
};

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  console.log('Blog post detail API called for slug:', params.slug);

  try {
    const dbConnection = await dbConnect();

    let post = null;
    let relatedPosts = [];

    if (dbConnection) {
      try {
        // Find the post by slug
        post = await BlogPost.findOne({
          slug: params.slug,
          status: 'published'
        })
          .populate('author', 'name email avatar')
          .lean();

        if (post) {
          // Increment view count
          await BlogPost.findByIdAndUpdate((post as any)._id, {
            $inc: { views: 1 }
          });

          // Get related posts (same category, excluding current post)
          relatedPosts = await BlogPost.find({
            _id: { $ne: (post as any)._id },
            category: (post as any).category,
            status: 'published'
          })
            .select('title slug excerpt publishedAt')
            .sort({ publishedAt: -1 })
            .limit(3)
            .lean();
        }
      } catch (dbError) {
        console.error('Database query failed:', dbError);
      }
    }

    // Fallback to mock data if no database result
    if (!post) {
      const mockPost = MOCK_POST_DATA[params.slug as keyof typeof MOCK_POST_DATA];
      if (mockPost) {
        console.log('Using mock data for post:', params.slug);
        post = mockPost;

        // Mock related posts
        relatedPosts = Object.entries(MOCK_POST_DATA)
          .filter(([slug]) => slug !== params.slug)
          .slice(0, 2)
          .map(([slug, data]) => ({
            _id: data._id,
            title: data.title,
            slug,
            excerpt: data.excerpt,
            publishedAt: data.publishedAt
          }));
      }
    }

    if (!post) {
      console.log('Post not found:', params.slug);
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    console.log(`Post retrieved: ${(post as any).title}, related posts: ${relatedPosts.length}`);

    return NextResponse.json({
      post,
      relatedPosts,
    });

  } catch (error) {
    console.error('Blog post detail error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch post' },
      { status: 500 }
    );
  }
}