import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/src/lib/database';
import BlogPost from '@/src/lib/models/BlogPost';

// Mock blog posts for demo when database is unavailable
const MOCK_POSTS = [
  {
    _id: '1',
    title: 'How to Merge PDF Files Efficiently',
    slug: 'merge-pdf-files-efficiently',
    excerpt: 'Learn how to efficiently merge multiple PDF files into a single document with our comprehensive guide and best practices.',
    author: { name: 'Admin User', email: '<EMAIL>' },
    category: 'tutorials',
    tags: ['pdf', 'merge', 'tools', 'guide'],
    featured: true,
    views: 342,
    publishedAt: '2024-01-15T00:00:00.000Z',
    status: 'published'
  },
  {
    _id: '2',
    title: 'Best Practices for PDF Compression',
    slug: 'pdf-compression-best-practices',
    excerpt: 'Discover the best practices for compressing PDF files to reduce size while maintaining quality for various use cases.',
    author: { name: 'Admin User', email: '<EMAIL>' },
    category: 'optimization',
    tags: ['pdf', 'compression', 'optimization', 'quality'],
    featured: true,
    views: 289,
    publishedAt: '2024-01-20T00:00:00.000Z',
    status: 'published'
  },
  {
    _id: '3',
    title: 'Converting Images to PDF: Complete Guide',
    slug: 'converting-images-to-pdf-guide',
    excerpt: 'Learn how to convert various image formats to PDF with our comprehensive guide covering best practices and optimization tips.',
    author: { name: 'Admin User', email: '<EMAIL>' },
    category: 'conversion',
    tags: ['images', 'pdf', 'conversion', 'guide'],
    featured: false,
    views: 156,
    publishedAt: '2024-01-25T00:00:00.000Z',
    status: 'published'
  },
  {
    _id: '4',
    title: 'PDF Security: Password Protection Guide',
    slug: 'pdf-security-password-protection',
    excerpt: 'Comprehensive guide to PDF security features including password protection, encryption levels, and permission controls.',
    author: { name: 'Admin User', email: '<EMAIL>' },
    category: 'security',
    tags: ['pdf', 'security', 'password', 'protection'],
    featured: false,
    views: 203,
    publishedAt: '2024-02-01T00:00:00.000Z',
    status: 'published'
  },
  {
    _id: '5',
    title: 'Splitting PDFs: When and How to Extract Pages',
    slug: 'splitting-pdfs-extract-pages',
    excerpt: 'Learn when and how to split PDF documents effectively, including best practices for page extraction and document organization.',
    author: { name: 'Admin User', email: '<EMAIL>' },
    category: 'tools',
    tags: ['pdf', 'split', 'extract', 'organization'],
    featured: false,
    views: 178,
    publishedAt: '2024-02-05T00:00:00.000Z',
    status: 'published'
  }
];

export async function GET(request: NextRequest) {
  console.log('Blog posts API called');

  try {
    const dbConnection = await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const search = searchParams.get('search');

    console.log('Query params:', { page, limit, category, featured, search });

    let posts = MOCK_POSTS;
    let total = MOCK_POSTS.length;

    if (dbConnection) {
      try {
        // Build query
        const query: any = { status: 'published' };

        if (category && category !== 'all') {
          query.category = category;
        }

        if (featured === 'true') {
          query.featured = true;
        }

        if (search) {
          query.$text = { $search: search };
        }

        // Calculate skip for pagination
        const skip = (page - 1) * limit;

        // Fetch posts with author populated
        const dbPosts = await BlogPost.find(query)
          .populate('author', 'name email avatar')
          .sort({ featured: -1, publishedAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean();

        // Get total count for pagination
        const dbTotal = await BlogPost.countDocuments(query);

        if (dbPosts.length > 0) {
          posts = dbPosts;
          total = dbTotal;
        }
      } catch (dbError) {
        console.error('Database query failed, using mock data:', dbError);
      }
    }

    // Apply filters to mock data if needed
    if (!dbConnection || posts === MOCK_POSTS) {
      let filteredPosts = [...MOCK_POSTS];

      if (category && category !== 'all') {
        filteredPosts = filteredPosts.filter(post => post.category === category);
      }

      if (featured === 'true') {
        filteredPosts = filteredPosts.filter(post => post.featured);
      }

      if (search) {
        filteredPosts = filteredPosts.filter(post =>
          post.title.toLowerCase().includes(search.toLowerCase()) ||
          post.excerpt.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Apply pagination
      const skip = (page - 1) * limit;
      posts = filteredPosts.slice(skip, skip + limit);
      total = filteredPosts.length;
    }

    console.log(`Retrieved ${posts.length} posts out of ${total} total`);

    return NextResponse.json({
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Blog posts fetch error:', error);

    // Return mock data as fallback
    return NextResponse.json({
      posts: MOCK_POSTS,
      pagination: {
        page: 1,
        limit: 10,
        total: MOCK_POSTS.length,
        pages: 1,
      },
    });
  }
}

export async function POST(request: NextRequest) {
  console.log('Create blog post API called');

  try {
    const dbConnection = await dbConnect();

    if (!dbConnection) {
      return NextResponse.json(
        { error: 'Database not available in demo mode' },
        { status: 503 }
      );
    }

    const body = await request.json();
    const {
      title,
      content,
      excerpt,
      category,
      tags,
      featuredImage,
      featured,
      status,
      seoTitle,
      seoDescription
    } = body;

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Use demo admin ID for author
    const authorId = 'demo-admin-id';

    const post = await BlogPost.create({
      title,
      slug,
      content,
      excerpt,
      author: authorId,
      category: category.toLowerCase(),
      tags: Array.isArray(tags) ? tags.map((tag: string) => tag.toLowerCase()) : [],
      featuredImage,
      featured: featured || false,
      status: status || 'draft',
      seoTitle,
      seoDescription,
      publishedAt: status === 'published' ? new Date() : undefined,
    });

    console.log('Blog post created:', post.slug);

    return NextResponse.json({ post }, { status: 201 });

  } catch (error) {
    console.error('Blog post creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create post' },
      { status: 500 }
    );
  }
}