import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { Document, Packer, Paragraph, TextRun } from 'docx';
import PptxGenJS from 'pptxgenjs';
import sharp from 'sharp';
import J<PERSON>Z<PERSON> from 'jszip';

// Types for file validation
interface FileValidation {
  isValid: boolean;
  error?: string;
  mimeType?: string;
  size?: number;
}

// Supported operations
const SUPPORTED_OPERATIONS = [
  'merge', 'split', 'compress', 'word-to-pdf', 'powerpoint-to-pdf',
  'excel-to-pdf', 'html-to-pdf', 'pdf-to-images', 'pdf-to-word',
  'pdf-to-powerpoint', 'pdf-to-excel', 'pdf-to-pdfa', 'protect',
  'extract', 'images-to-pdf'
] as const;

type Operation = typeof SUPPORTED_OPERATIONS[number];

// MIME type mappings
const MIME_TYPES = {
  pdf: 'application/pdf',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  doc: 'application/msword',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  xls: 'application/vnd.ms-excel',
  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ppt: 'application/vnd.ms-powerpoint',
  html: 'text/html',
  jpg: 'image/jpeg',
  jpeg: 'image/jpeg',
  png: 'image/png',
  gif: 'image/gif',
  bmp: 'image/bmp',
  tiff: 'image/tiff',
  zip: 'application/zip'
};

// File size limits (in bytes)
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const MAX_TOTAL_SIZE = 100 * 1024 * 1024; // 100MB for multiple files

/**
 * Validates uploaded files based on operation requirements
 */
function validateFiles(files: File[], operation: Operation): FileValidation {
  if (!files || files.length === 0) {
    return { isValid: false, error: 'No files provided' };
  }

  // Check total size
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  if (totalSize > MAX_TOTAL_SIZE) {
    return { isValid: false, error: `Total file size exceeds ${MAX_TOTAL_SIZE / 1024 / 1024}MB limit` };
  }

  // Check individual file sizes
  for (const file of files) {
    if (file.size > MAX_FILE_SIZE) {
      return { isValid: false, error: `File ${file.name} exceeds ${MAX_FILE_SIZE / 1024 / 1024}MB limit` };
    }
  }

  // Operation-specific validation
  switch (operation) {
    case 'word-to-pdf':
      return validateWordFiles(files);
    case 'excel-to-pdf':
      return validateExcelFiles(files);
    case 'powerpoint-to-pdf':
      return validatePowerPointFiles(files);
    case 'html-to-pdf':
      return validateHtmlFiles(files);
    case 'images-to-pdf':
      return validateImageFiles(files);
    case 'pdf-to-images':
    case 'pdf-to-word':
    case 'pdf-to-powerpoint':
    case 'pdf-to-excel':
    case 'pdf-to-pdfa':
    case 'merge':
    case 'split':
    case 'compress':
    case 'protect':
    case 'extract':
      return validatePdfFiles(files);
    default:
      return { isValid: false, error: `Unsupported operation: ${operation}` };
  }
}

function validateWordFiles(files: File[]): FileValidation {
  const validTypes = [MIME_TYPES.docx, MIME_TYPES.doc];
  for (const file of files) {
    if (!validTypes.includes(file.type as any)) {
      return { isValid: false, error: `Invalid file type: ${file.name}. Expected Word document (.docx, .doc)` };
    }
  }
  return { isValid: true };
}

function validateExcelFiles(files: File[]): FileValidation {
  const validTypes = [MIME_TYPES.xlsx, MIME_TYPES.xls];
  for (const file of files) {
    if (!validTypes.includes(file.type as any)) {
      return { isValid: false, error: `Invalid file type: ${file.name}. Expected Excel file (.xlsx, .xls)` };
    }
  }
  return { isValid: true };
}

function validatePowerPointFiles(files: File[]): FileValidation {
  const validTypes = [MIME_TYPES.pptx, MIME_TYPES.ppt];
  for (const file of files) {
    if (!validTypes.includes(file.type as any)) {
      return { isValid: false, error: `Invalid file type: ${file.name}. Expected PowerPoint file (.pptx, .ppt)` };
    }
  }
  return { isValid: true };
}

function validateHtmlFiles(files: File[]): FileValidation {
  const validTypes = [MIME_TYPES.html, 'text/plain'];
  for (const file of files) {
    if (!validTypes.includes(file.type as any)) {
      return { isValid: false, error: `Invalid file type: ${file.name}. Expected HTML file (.html)` };
    }
  }
  return { isValid: true };
}

function validateImageFiles(files: File[]): FileValidation {
  const validTypes = [MIME_TYPES.jpg, MIME_TYPES.jpeg, MIME_TYPES.png, MIME_TYPES.gif, MIME_TYPES.bmp, MIME_TYPES.tiff];
  for (const file of files) {
    if (!validTypes.includes(file.type as any)) {
      return { isValid: false, error: `Invalid file type: ${file.name}. Expected image file (.jpg, .png, .gif, .bmp, .tiff)` };
    }
  }
  return { isValid: true };
}

function validatePdfFiles(files: File[]): FileValidation {
  for (const file of files) {
    if (file.type !== MIME_TYPES.pdf) {
      return { isValid: false, error: `Invalid file type: ${file.name}. Expected PDF file` };
    }
  }
  return { isValid: true };
}

/**
 * Main POST handler for PDF processing operations
 */
export async function POST(request: NextRequest) {
  console.log('PDF processing request received');

  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const operation = formData.get('operation') as Operation;

    console.log(`Processing operation: ${operation} with ${files.length} files`);

    // Validate operation
    if (!SUPPORTED_OPERATIONS.includes(operation)) {
      return NextResponse.json(
        { error: `Unsupported operation: ${operation}` },
        { status: 400 }
      );
    }

    // Validate files
    const validation = validateFiles(files, operation);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    // Process based on operation
    let result: { buffer: Buffer; filename: string; contentType: string };

    switch (operation) {
      case 'word-to-pdf':
        result = await convertWordToPdf(files);
        break;
      case 'excel-to-pdf':
        result = await convertExcelToPdf(files);
        break;
      case 'powerpoint-to-pdf':
        result = await convertPowerPointToPdf(files);
        break;
      case 'html-to-pdf':
        result = await convertHtmlToPdf(files);
        break;
      case 'images-to-pdf':
        result = await convertImagesToPdf(files);
        break;
      case 'pdf-to-images':
        result = await convertPdfToImages(files[0]);
        break;
      case 'pdf-to-word':
        result = await convertPdfToWord(files[0]);
        break;
      case 'pdf-to-powerpoint':
        result = await convertPdfToPowerPoint(files[0]);
        break;
      case 'pdf-to-excel':
        result = await convertPdfToExcel(files[0]);
        break;
      case 'pdf-to-pdfa':
        result = await convertPdfToPdfA(files);
        break;
      case 'merge':
        result = await mergePdfs(files);
        break;
      case 'split':
        result = await splitPdf(files[0]);
        break;
      case 'compress':
        result = await compressPdf(files[0]);
        break;
      case 'protect':
        result = await protectPdf(files[0]);
        break;
      case 'extract':
        result = await extractTextFromPdf(files[0]);
        break;
      default:
        throw new Error(`Operation ${operation} not implemented`);
    }

    console.log(`Operation ${operation} completed successfully`);

    // Return the processed file with proper headers
    return new NextResponse(result.buffer, {
      status: 200,
      headers: {
        'Content-Type': result.contentType,
        'Content-Disposition': `attachment; filename="${result.filename}"`,
        'Content-Length': result.buffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('PDF processing error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Processing failed' },
      { status: 500 }
    );
  }
}

/**
 * Convert Word documents to PDF
 */
async function convertWordToPdf(files: File[]): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting ${files.length} Word document(s) to PDF`);

  const pdfDoc = await PDFDocument.create();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

  for (const file of files) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Extract text and basic formatting from Word document
      const result = await mammoth.extractRawText({ buffer });
      let text = result.value;

      if (!text || text.trim().length === 0) {
        throw new Error(`No text content found in ${file.name}`);
      }

      // Clean text to remove problematic characters that WinAnsi can't encode
      text = text
        .replace(/\t/g, '    ') // Replace tabs with 4 spaces
        .replace(/[\u0000-\u0008\u000B-\u000C\u000E-\u001F\u007F-\u009F]/g, '') // Remove control characters
        .replace(/[^\u0020-\u007E\u00A0-\u00FF]/g, '?'); // Replace non-WinAnsi characters with ?

      // Split text into paragraphs
      const paragraphs = text.split('\n').filter(p => p.trim().length > 0);

      // Create pages and add content
      let currentPage = pdfDoc.addPage([612, 792]); // Letter size
      let yPosition = 750;
      const margin = 50;
      const lineHeight = 14;
      const maxWidth = 512; // Page width minus margins

      for (const paragraph of paragraphs) {
        // Handle long paragraphs by wrapping text
        const words = paragraph.split(' ');
        let currentLine = '';

        for (const word of words) {
          const testLine = currentLine ? `${currentLine} ${word}` : word;
          const textWidth = font.widthOfTextAtSize(testLine, 12);

          if (textWidth > maxWidth && currentLine) {
            // Draw current line and start new one
            currentPage.drawText(currentLine, {
              x: margin,
              y: yPosition,
              size: 12,
              font: font,
              color: rgb(0, 0, 0),
            });

            yPosition -= lineHeight;
            currentLine = word;

            // Check if we need a new page
            if (yPosition < 50) {
              currentPage = pdfDoc.addPage([612, 792]);
              yPosition = 750;
            }
          } else {
            currentLine = testLine;
          }
        }

        // Draw the remaining text
        if (currentLine) {
          currentPage.drawText(currentLine, {
            x: margin,
            y: yPosition,
            size: 12,
            font: font,
            color: rgb(0, 0, 0),
          });

          yPosition -= lineHeight * 1.5; // Extra space between paragraphs

          // Check if we need a new page
          if (yPosition < 50) {
            currentPage = pdfDoc.addPage([612, 792]);
            yPosition = 750;
          }
        }
      }

    } catch (error) {
      console.error(`Error processing ${file.name}:`, error);
      throw new Error(`Failed to process Word document ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  const pdfBytes = await pdfDoc.save();
  const filename = files.length === 1 ?
    `${files[0].name.replace(/\.[^/.]+$/, '')}.pdf` :
    'converted-documents.pdf';

  console.log(`Word to PDF conversion completed: ${pdfBytes.length} bytes`);

  return {
    buffer: Buffer.from(pdfBytes),
    filename,
    contentType: MIME_TYPES.pdf
  };
}

/**
 * Convert Excel files to PDF
 */
async function convertExcelToPdf(files: File[]): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting ${files.length} Excel file(s) to PDF`);

  const pdfDoc = await PDFDocument.create();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  for (const file of files) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });

      // Process each worksheet
      for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' }) as any[][];

        if (jsonData.length === 0) continue;

        // Create a new page for each sheet
        const page = pdfDoc.addPage([792, 612]); // Landscape for better table display
        let yPosition = 570;
        const margin = 30;
        const cellHeight = 20;
        const cellWidth = 100;

        // Draw sheet title
        page.drawText(`Sheet: ${sheetName}`, {
          x: margin,
          y: yPosition,
          size: 14,
          font: boldFont,
          color: rgb(0, 0, 0),
        });

        yPosition -= 30;

        // Draw table data
        for (let rowIndex = 0; rowIndex < Math.min(jsonData.length, 25); rowIndex++) { // Limit rows to fit page
          const row = jsonData[rowIndex];
          let xPosition = margin;

          for (let colIndex = 0; colIndex < Math.min(row.length, 7); colIndex++) { // Limit columns to fit page
            const cellValue = String(row[colIndex] || '').substring(0, 15); // Truncate long values

            // Draw cell border
            page.drawRectangle({
              x: xPosition,
              y: yPosition - cellHeight,
              width: cellWidth,
              height: cellHeight,
              borderColor: rgb(0, 0, 0),
              borderWidth: 1,
            });

            // Draw cell content
            if (cellValue) {
              page.drawText(cellValue, {
                x: xPosition + 5,
                y: yPosition - 15,
                size: 10,
                font: rowIndex === 0 ? boldFont : font, // Bold for header row
                color: rgb(0, 0, 0),
              });
            }

            xPosition += cellWidth;
          }

          yPosition -= cellHeight;

          // Check if we need a new page
          if (yPosition < 50) {
            break; // Stop adding rows if we run out of space
          }
        }
      }

    } catch (error) {
      console.error(`Error processing ${file.name}:`, error);
      throw new Error(`Failed to process Excel file ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  const pdfBytes = await pdfDoc.save();
  const filename = files.length === 1 ?
    `${files[0].name.replace(/\.[^/.]+$/, '')}.pdf` :
    'converted-spreadsheets.pdf';

  console.log(`Excel to PDF conversion completed: ${pdfBytes.length} bytes`);

  return {
    buffer: Buffer.from(pdfBytes),
    filename,
    contentType: MIME_TYPES.pdf
  };
}

/**
 * Convert PowerPoint files to PDF
 */
async function convertPowerPointToPdf(files: File[]): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting ${files.length} PowerPoint file(s) to PDF`);

  const pdfDoc = await PDFDocument.create();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  for (const file of files) {
    try {
      // For PowerPoint files, we'll create a placeholder PDF with file info
      // In production, you'd use a library like node-pptx or similar to extract actual content
      const page = pdfDoc.addPage([612, 792]);
      const margin = 50;
      let yPosition = 750;

      // Title
      page.drawText('PowerPoint Conversion', {
        x: margin,
        y: yPosition,
        size: 18,
        font: boldFont,
        color: rgb(0, 0, 0),
      });

      yPosition -= 40;

      // File information
      page.drawText(`File: ${file.name}`, {
        x: margin,
        y: yPosition,
        size: 12,
        font: font,
        color: rgb(0, 0, 0),
      });

      yPosition -= 20;

      page.drawText(`Size: ${(file.size / 1024).toFixed(2)} KB`, {
        x: margin,
        y: yPosition,
        size: 12,
        font: font,
        color: rgb(0, 0, 0),
      });

      yPosition -= 40;

      // Note about PowerPoint processing
      const noteText = [
        'PowerPoint file has been processed.',
        'This conversion extracts basic file information.',
        '',
        'For full PowerPoint content extraction, consider using:',
        '• PptxGenJS for reading PPTX files',
        '• node-pptx for comprehensive parsing',
        '• Custom PowerPoint processing libraries',
        '',
        'The actual slide content, images, and formatting',
        'would be extracted and rendered in a production system.'
      ];

      for (const line of noteText) {
        page.drawText(line, {
          x: margin,
          y: yPosition,
          size: 11,
          font: font,
          color: rgb(0.2, 0.2, 0.2),
        });
        yPosition -= 16;
      }

    } catch (error) {
      console.error(`Error processing ${file.name}:`, error);
      throw new Error(`Failed to process PowerPoint file ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  const pdfBytes = await pdfDoc.save();
  const filename = files.length === 1 ?
    `${files[0].name.replace(/\.[^/.]+$/, '')}.pdf` :
    'converted-presentations.pdf';

  console.log(`PowerPoint to PDF conversion completed: ${pdfBytes.length} bytes`);

  return {
    buffer: Buffer.from(pdfBytes),
    filename,
    contentType: MIME_TYPES.pdf
  };
}

/**
 * Convert HTML to PDF
 */
async function convertHtmlToPdf(files: File[]): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting ${files.length} HTML file(s) to PDF`);

  try {
    const htmlContent = await files[0].text();

    // Create PDF with HTML content rendered as text
    // In production, you'd use puppeteer or similar for proper HTML rendering
    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Strip HTML tags for basic text extraction
    let textContent = htmlContent
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Clean text to remove problematic characters that WinAnsi can't encode
    textContent = textContent
      .replace(/\t/g, '    ') // Replace tabs with 4 spaces
      .replace(/[\u0000-\u0008\u000B-\u000C\u000E-\u001F\u007F-\u009F]/g, '') // Remove control characters
      .replace(/[^\u0020-\u007E\u00A0-\u00FF]/g, '?'); // Replace non-WinAnsi characters with ?

    if (!textContent) {
      throw new Error('No text content found in HTML');
    }

    // Create pages and add content
    let currentPage = pdfDoc.addPage([612, 792]); // Letter size
    let yPosition = 750;
    const margin = 50;
    const lineHeight = 14;
    const maxWidth = 512; // Page width minus margins

    // Add title
    currentPage.drawText('HTML Document Conversion', {
      x: margin,
      y: yPosition,
      size: 16,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    yPosition -= 30;

    // Split text into paragraphs
    const paragraphs = textContent.split(/\n+/).filter(p => p.trim().length > 0);

    for (const paragraph of paragraphs) {
      // Handle long paragraphs by wrapping text
      const words = paragraph.split(' ');
      let currentLine = '';

      for (const word of words) {
        const testLine = currentLine ? `${currentLine} ${word}` : word;
        const textWidth = font.widthOfTextAtSize(testLine, 12);

        if (textWidth > maxWidth && currentLine) {
          // Draw current line and start new one
          currentPage.drawText(currentLine, {
            x: margin,
            y: yPosition,
            size: 12,
            font: font,
            color: rgb(0, 0, 0),
          });

          yPosition -= lineHeight;
          currentLine = word;

          // Check if we need a new page
          if (yPosition < 50) {
            currentPage = pdfDoc.addPage([612, 792]);
            yPosition = 750;
          }
        } else {
          currentLine = testLine;
        }
      }

      // Draw the remaining text
      if (currentLine) {
        currentPage.drawText(currentLine, {
          x: margin,
          y: yPosition,
          size: 12,
          font: font,
          color: rgb(0, 0, 0),
        });

        yPosition -= lineHeight * 1.5; // Extra space between paragraphs

        // Check if we need a new page
        if (yPosition < 50) {
          currentPage = pdfDoc.addPage([612, 792]);
          yPosition = 750;
        }
      }
    }

    const pdfBytes = await pdfDoc.save();
    const filename = `${files[0].name.replace(/\.[^/.]+$/, '')}.pdf`;

    console.log(`HTML to PDF conversion completed: ${pdfBytes.length} bytes`);

    return {
      buffer: Buffer.from(pdfBytes),
      filename,
      contentType: MIME_TYPES.pdf
    };

  } catch (error) {
    console.error('HTML to PDF conversion error:', error);
    throw new Error(`Failed to convert HTML to PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert images to PDF
 */
async function convertImagesToPdf(files: File[]): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting ${files.length} image(s) to PDF`);

  const pdfDoc = await PDFDocument.create();

  for (const file of files) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const imageBytes = new Uint8Array(arrayBuffer);

      let image;
      if (file.type === 'image/jpeg' || file.type === 'image/jpg') {
        image = await pdfDoc.embedJpg(imageBytes);
      } else if (file.type === 'image/png') {
        image = await pdfDoc.embedPng(imageBytes);
      } else {
        // Convert other formats to PNG using Sharp
        const pngBuffer = await sharp(imageBytes).png().toBuffer();
        image = await pdfDoc.embedPng(pngBuffer);
      }

      // Calculate dimensions to fit page while maintaining aspect ratio
      const page = pdfDoc.addPage();
      const { width: pageWidth, height: pageHeight } = page.getSize();
      const margin = 50;
      const maxWidth = pageWidth - (margin * 2);
      const maxHeight = pageHeight - (margin * 2);

      const { width: imgWidth, height: imgHeight } = image.scale(1);
      const widthRatio = maxWidth / imgWidth;
      const heightRatio = maxHeight / imgHeight;
      const scale = Math.min(widthRatio, heightRatio);

      const scaledWidth = imgWidth * scale;
      const scaledHeight = imgHeight * scale;

      // Center the image on the page
      const x = (pageWidth - scaledWidth) / 2;
      const y = (pageHeight - scaledHeight) / 2;

      page.drawImage(image, {
        x,
        y,
        width: scaledWidth,
        height: scaledHeight,
      });

    } catch (error) {
      console.error(`Error processing image ${file.name}:`, error);
      throw new Error(`Failed to process image ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  const pdfBytes = await pdfDoc.save();
  const filename = files.length === 1 ?
    `${files[0].name.replace(/\.[^/.]+$/, '')}.pdf` :
    'images-to-pdf.pdf';

  console.log(`Images to PDF conversion completed: ${pdfBytes.length} bytes`);

  return {
    buffer: Buffer.from(pdfBytes),
    filename,
    contentType: MIME_TYPES.pdf
  };
}

/**
 * Convert PDF to images
 */
async function convertPdfToImages(file: File): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting PDF to images: ${file.name}`);

  try {
    // For now, create a simple placeholder implementation
    // In production, you'd use pdf2pic or similar library to extract actual pages
    const zip = new JSZip();

    // Create a placeholder image for each page
    // This is a simplified implementation - in production you'd extract actual pages
    const placeholderImage = await sharp({
      create: {
        width: 800,
        height: 1200,
        channels: 3,
        background: { r: 255, g: 255, b: 255 }
      }
    })
      .jpeg()
      .toBuffer();

    // Add placeholder for first page (in production, you'd loop through actual pages)
    zip.file('page-1.jpg', placeholderImage);

    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });
    const filename = `${file.name.replace(/\.[^/.]+$/, '')}-images.zip`;

    console.log(`PDF to images conversion completed: ${zipBuffer.length} bytes`);

    return {
      buffer: zipBuffer,
      filename,
      contentType: MIME_TYPES.zip
    };

  } catch (error) {
    console.error('PDF to images conversion error:', error);
    throw new Error(`Failed to convert PDF to images: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert PDF to Word document
 */
async function convertPdfToWord(file: File): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting PDF to Word: ${file.name}`);

  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdfBuffer = Buffer.from(arrayBuffer);

    // Extract text from PDF using pdf-lib
    const pdf = await PDFDocument.load(pdfBuffer);
    const pages = pdf.getPages();

    // For now, create a simple text representation
    // In production, you'd use a proper PDF text extraction library
    const text = `PDF Document with ${pages.length} pages\nConverted from: ${file.name}`;

    if (!text || text.trim().length === 0) {
      throw new Error('No text content found in PDF');
    }

    // Create Word document
    const doc = new Document({
      sections: [{
        properties: {},
        children: text.split('\n').map((line: string) =>
          new Paragraph({
            children: [new TextRun(line.trim() || ' ')], // Ensure non-empty paragraphs
          })
        ),
      }],
    });

    const docxBuffer = await Packer.toBuffer(doc);
    const filename = `${file.name.replace(/\.[^/.]+$/, '')}.docx`;

    console.log(`PDF to Word conversion completed: ${docxBuffer.length} bytes`);

    return {
      buffer: docxBuffer,
      filename,
      contentType: MIME_TYPES.docx
    };

  } catch (error) {
    console.error('PDF to Word conversion error:', error);
    throw new Error(`Failed to convert PDF to Word: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert PDF to PowerPoint
 */
async function convertPdfToPowerPoint(file: File): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting PDF to PowerPoint: ${file.name}`);

  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdfBuffer = Buffer.from(arrayBuffer);

    // Extract text from PDF using pdf-lib
    const pdf = await PDFDocument.load(pdfBuffer);
    const pages = pdf.getPages();

    // For now, create a simple text representation
    // In production, you'd use a proper PDF text extraction library
    const text = `PDF Document with ${pages.length} pages\nConverted from: ${file.name}`;

    if (!text || text.trim().length === 0) {
      throw new Error('No text content found in PDF');
    }

    // Create PowerPoint presentation
    const pptx = new PptxGenJS();

    // Split text into slides (by page breaks or paragraphs)
    const slides = text.split('\f').length > 1 ? text.split('\f') : [text]; // Split by form feed or use entire text

    for (let i = 0; i < slides.length; i++) {
      const slideText = slides[i].trim();
      if (slideText) {
        const slide = pptx.addSlide();

        // Add title (first line or slide number)
        const lines = slideText.split('\n').filter((line: string) => line.trim());
        const title = lines[0] || `Slide ${i + 1}`;

        slide.addText(title, {
          x: 0.5,
          y: 0.5,
          w: 9,
          h: 1,
          fontSize: 24,
          bold: true,
          color: '363636'
        });

        // Add content (remaining lines)
        const content = lines.slice(1).join('\n') || slideText;
        if (content.trim()) {
          slide.addText(content, {
            x: 0.5,
            y: 2,
            w: 9,
            h: 5,
            fontSize: 14,
            color: '363636'
          });
        }
      }
    }

    // If no slides were created, create a default slide
    // Note: PptxGenJS doesn't expose slides array, so we'll always create at least one slide
    if (slides.length === 0 || !slides[0].trim()) {
      const slide = pptx.addSlide();
      slide.addText('Converted from PDF', {
        x: 0.5,
        y: 0.5,
        w: 9,
        h: 1,
        fontSize: 24,
        bold: true,
        color: '363636'
      });

      slide.addText(text.substring(0, 500) + (text.length > 500 ? '...' : ''), {
        x: 0.5,
        y: 2,
        w: 9,
        h: 5,
        fontSize: 14,
        color: '363636'
      });
    }

    const pptxBuffer = await pptx.write({ outputType: 'nodebuffer' }) as Buffer;
    const filename = `${file.name.replace(/\.[^/.]+$/, '')}.pptx`;

    console.log(`PDF to PowerPoint conversion completed: ${pptxBuffer.length} bytes`);

    return {
      buffer: pptxBuffer,
      filename,
      contentType: MIME_TYPES.pptx
    };

  } catch (error) {
    console.error('PDF to PowerPoint conversion error:', error);
    throw new Error(`Failed to convert PDF to PowerPoint: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert PDF to Excel
 */
async function convertPdfToExcel(file: File): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting PDF to Excel: ${file.name}`);

  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdfBuffer = Buffer.from(arrayBuffer);

    // Extract text from PDF using pdf-lib
    const pdf = await PDFDocument.load(pdfBuffer);
    const pages = pdf.getPages();

    // For now, create a simple text representation
    // In production, you'd use a proper PDF text extraction library
    const text = `PDF Document with ${pages.length} pages\nConverted from: ${file.name}`;

    if (!text || text.trim().length === 0) {
      throw new Error('No text content found in PDF');
    }

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Split text into lines and try to detect tabular data
    const lines = text.split('\n').filter((line: string) => line.trim());
    const data: string[][] = [];

    // Add header
    data.push(['Line Number', 'Content']);

    // Add content line by line
    lines.forEach((line: string, index: number) => {
      // Try to split by common delimiters
      const cells = line.includes('\t') ? line.split('\t') :
        line.includes('  ') ? line.split(/\s{2,}/) :
          [line];

      if (cells.length > 1) {
        // Looks like tabular data
        data.push(cells.map((cell: string) => cell.trim()));
      } else {
        // Regular text line
        data.push([String(index + 1), line.trim()]);
      }
    });

    // Create worksheet
    const worksheet = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'PDF Content');

    // Generate Excel buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    const filename = `${file.name.replace(/\.[^/.]+$/, '')}.xlsx`;

    console.log(`PDF to Excel conversion completed: ${excelBuffer.length} bytes`);

    return {
      buffer: excelBuffer,
      filename,
      contentType: MIME_TYPES.xlsx
    };

  } catch (error) {
    console.error('PDF to Excel conversion error:', error);
    throw new Error(`Failed to convert PDF to Excel: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert PDF to PDF/A format
 */
async function convertPdfToPdfA(files: File[]): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Converting ${files.length} PDF(s) to PDF/A format`);

  try {
    // For PDF/A conversion, we'll create a new PDF with PDF/A compliance
    // In production, you'd use specialized libraries for proper PDF/A conversion
    const pdfDoc = await PDFDocument.create();

    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer();
      const existingPdfBytes = new Uint8Array(arrayBuffer);
      const existingPdf = await PDFDocument.load(existingPdfBytes);

      // Copy all pages from existing PDF
      const pages = await pdfDoc.copyPages(existingPdf, existingPdf.getPageIndices());
      pages.forEach((page) => pdfDoc.addPage(page));
    }

    // Add PDF/A metadata (simplified)
    pdfDoc.setTitle('PDF/A Document');
    pdfDoc.setSubject('Converted to PDF/A format');
    pdfDoc.setCreator('PDF Tools');
    pdfDoc.setProducer('PDF Tools PDF/A Converter');

    const pdfBytes = await pdfDoc.save();
    const filename = files.length === 1 ?
      `${files[0].name.replace(/\.[^/.]+$/, '')}-pdfa.pdf` :
      'converted-pdfa.pdf';

    console.log(`PDF to PDF/A conversion completed: ${pdfBytes.length} bytes`);

    return {
      buffer: Buffer.from(pdfBytes),
      filename,
      contentType: MIME_TYPES.pdf
    };

  } catch (error) {
    console.error('PDF to PDF/A conversion error:', error);
    throw new Error(`Failed to convert to PDF/A: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Merge multiple PDFs into one
 */
async function mergePdfs(files: File[]): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Merging ${files.length} PDF files`);

  try {
    const mergedPdf = await PDFDocument.create();

    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer();
      const pdfBytes = new Uint8Array(arrayBuffer);
      const pdf = await PDFDocument.load(pdfBytes);

      // Copy all pages from this PDF
      const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
      pages.forEach((page) => mergedPdf.addPage(page));
    }

    // Set metadata
    mergedPdf.setTitle('Merged PDF Document');
    mergedPdf.setCreator('PDF Tools');
    mergedPdf.setProducer('PDF Tools Merger');

    const pdfBytes = await mergedPdf.save();
    const filename = 'merged-document.pdf';

    console.log(`PDF merge completed: ${pdfBytes.length} bytes`);

    return {
      buffer: Buffer.from(pdfBytes),
      filename,
      contentType: MIME_TYPES.pdf
    };

  } catch (error) {
    console.error('PDF merge error:', error);
    throw new Error(`Failed to merge PDFs: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Split PDF into separate pages
 */
async function splitPdf(file: File): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Splitting PDF: ${file.name}`);

  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdfBytes = new Uint8Array(arrayBuffer);
    const pdf = await PDFDocument.load(pdfBytes);

    const pageCount = pdf.getPageCount();
    const zip = new JSZip();

    // Create separate PDF for each page
    for (let i = 0; i < pageCount; i++) {
      const newPdf = await PDFDocument.create();
      const [copiedPage] = await newPdf.copyPages(pdf, [i]);
      newPdf.addPage(copiedPage);

      // Set metadata for individual page
      newPdf.setTitle(`Page ${i + 1} of ${file.name}`);
      newPdf.setCreator('PDF Tools');
      newPdf.setProducer('PDF Tools Splitter');

      const pageBytes = await newPdf.save();
      zip.file(`page-${i + 1}.pdf`, pageBytes);
    }

    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });
    const filename = `${file.name.replace(/\.[^/.]+$/, '')}-split.zip`;

    console.log(`PDF split completed: ${zipBuffer.length} bytes, ${pageCount} pages`);

    return {
      buffer: zipBuffer,
      filename,
      contentType: MIME_TYPES.zip
    };

  } catch (error) {
    console.error('PDF split error:', error);
    throw new Error(`Failed to split PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Compress PDF by optimizing content
 */
async function compressPdf(file: File): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Compressing PDF: ${file.name}`);

  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdfBytes = new Uint8Array(arrayBuffer);
    const pdf = await PDFDocument.load(pdfBytes);

    // Basic compression by re-saving the PDF
    // pdf-lib automatically applies some optimization
    const compressedBytes = await pdf.save({
      useObjectStreams: false, // Can help with compression
      addDefaultPage: false,
    });

    const filename = `${file.name.replace(/\.[^/.]+$/, '')}-compressed.pdf`;
    const originalSize = file.size;
    const compressedSize = compressedBytes.length;
    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

    console.log(`PDF compression completed: ${originalSize} -> ${compressedSize} bytes (${compressionRatio}% reduction)`);

    return {
      buffer: Buffer.from(compressedBytes),
      filename,
      contentType: MIME_TYPES.pdf
    };

  } catch (error) {
    console.error('PDF compression error:', error);
    throw new Error(`Failed to compress PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Protect PDF with password
 */
async function protectPdf(file: File): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Protecting PDF: ${file.name}`);

  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdfBytes = new Uint8Array(arrayBuffer);
    const pdf = await PDFDocument.load(pdfBytes);

    // Note: pdf-lib doesn't support password protection directly
    // In production, you'd use a library like HummusJS or PDFtk
    // For now, we'll add a watermark to indicate protection

    const pages = pdf.getPages();
    const font = await pdf.embedFont(StandardFonts.Helvetica);

    pages.forEach(page => {
      const { width, height } = page.getSize();

      // Add watermark
      page.drawText('PROTECTED DOCUMENT', {
        x: width / 2 - 80,
        y: height / 2,
        size: 20,
        font,
        color: rgb(0.8, 0.8, 0.8),
        opacity: 0.3,
      });
    });

    // Set metadata
    pdf.setTitle('Protected PDF Document');
    pdf.setCreator('PDF Tools');
    pdf.setProducer('PDF Tools Protector');

    const protectedBytes = await pdf.save();
    const filename = `${file.name.replace(/\.[^/.]+$/, '')}-protected.pdf`;

    console.log(`PDF protection completed: ${protectedBytes.length} bytes`);

    return {
      buffer: Buffer.from(protectedBytes),
      filename,
      contentType: MIME_TYPES.pdf
    };

  } catch (error) {
    console.error('PDF protection error:', error);
    throw new Error(`Failed to protect PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Extract text from PDF
 */
async function extractTextFromPdf(file: File): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
  console.log(`Extracting text from PDF: ${file.name}`);

  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdfBuffer = Buffer.from(arrayBuffer);

    // Extract text using pdf-lib
    const pdf = await PDFDocument.load(pdfBuffer);
    const pages = pdf.getPages();

    // For now, create a simple text representation
    // In production, you'd use a proper PDF text extraction library
    const text = `PDF Document with ${pages.length} pages\nFile: ${file.name}\nSize: ${file.size} bytes`;

    if (!text || text.trim().length === 0) {
      throw new Error('No text content found in PDF');
    }

    // Create a formatted text output
    const extractedContent = [
      '='.repeat(60),
      `TEXT EXTRACTED FROM: ${file.name}`,
      `EXTRACTION DATE: ${new Date().toISOString()}`,
      `TOTAL PAGES: ${pages.length}`,
      `TOTAL CHARACTERS: ${text.length}`,
      '='.repeat(60),
      '',
      text,
      '',
      '='.repeat(60),
      'END OF EXTRACTED TEXT',
      '='.repeat(60)
    ].join('\n');

    const textBuffer = Buffer.from(extractedContent, 'utf-8');
    const filename = `${file.name.replace(/\.[^/.]+$/, '')}-extracted.txt`;

    console.log(`Text extraction completed: ${textBuffer.length} bytes, ${pages.length} pages`);

    return {
      buffer: textBuffer,
      filename,
      contentType: 'text/plain'
    };

  } catch (error) {
    console.error('Text extraction error:', error);
    throw new Error(`Failed to extract text from PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}