import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "next-themes";
import Navigation from "@/src/components/layout/Navigation";
import { Toaster } from "@/src/components/ui/sonner";
import AuthProvider from "@/src/components/auth/AuthProvider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "PDFTools - Professional PDF Processing & Blog Platform",
    template: "%s | PDFTools"
  },
  description: "Comprehensive PDF tools for merging, splitting, compression, and conversion. Plus a full-featured blog platform with admin dashboard.",
  keywords: [
    "PDF tools",
    "PDF merge",
    "PDF split",
    "PDF compress",
    "PDF converter",
    "document processing",
    "file conversion",
    "online PDF tools",
    "free PDF tools",
    "blog platform",
    "content management"
  ],
  authors: [{ name: "PDFTools Team" }],
  creator: "PDFTools",
  publisher: "PDFTools",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://pdftools.com',
    siteName: 'PDFTools',
    title: 'PDFTools - Professional PDF Processing & Blog Platform',
    description: 'Comprehensive PDF tools for merging, splitting, compression, and conversion. Plus a full-featured blog platform with admin dashboard.',
    images: [
      {
        url: 'https://pdftools.com/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'PDFTools - Professional PDF Processing',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PDFTools - Professional PDF Processing & Blog Platform',
    description: 'Comprehensive PDF tools for merging, splitting, compression, and conversion.',
    images: ['https://pdftools.com/twitter-image.jpg'],
    creator: '@pdftools',
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
  alternates: {
    canonical: 'https://pdftools.com',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <AuthProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            disableTransitionOnChange
          >
            <div className="min-h-screen bg-background">
              <Navigation />
              <main className="relative">
                {children}
              </main>
              <Toaster />
            </div>
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  );
}