'use client';

import { motion } from 'framer-motion';
import {
  FileText,
  Scissors,
  Archive,
  Image,
  Shield,
  FileSearch,
  ArrowRight,
  Star,
  Users,
  Zap
} from 'lucide-react';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Badge } from '@/src/components/ui/badge';
import PdfToolCard from '@/src/components/pdf/PdfToolCard';
import Link from 'next/link';
import { HomePageStructuredData, OrganizationStructuredData } from '@/src/components/seo/StructuredData';

const pdfTools = [
  {
    title: 'Merge PDFs',
    description: 'Combine multiple PDF files into a single document with ease',
    icon: FileText,
    color: 'bg-gradient-to-br from-blue-500 to-blue-600',
    action: 'merge'
  },
  {
    title: 'Split PDFs',
    description: 'Extract specific pages or split documents into separate files',
    icon: Scissors,
    color: 'bg-gradient-to-br from-green-500 to-green-600',
    action: 'split'
  },
  {
    title: 'Compress PDFs',
    description: 'Reduce file size while maintaining quality for easy sharing',
    icon: Archive,
    color: 'bg-gradient-to-br from-purple-500 to-purple-600',
    action: 'compress'
  },
  {
    title: 'Images to PDF',
    description: 'Convert JPG, PNG and other images into PDF documents',
    icon: Image,
    color: 'bg-gradient-to-br from-orange-500 to-orange-600',
    action: 'images-to-pdf'
  },
  {
    title: 'Word to PDF',
    description: 'Convert Word documents (DOCX) to PDF format',
    icon: FileText,
    color: 'bg-gradient-to-br from-cyan-500 to-cyan-600',
    action: 'word-to-pdf'
  },
  {
    title: 'PowerPoint to PDF',
    description: 'Convert PowerPoint presentations (PPTX) to PDF format',
    icon: FileText,
    color: 'bg-gradient-to-br from-rose-500 to-rose-600',
    action: 'powerpoint-to-pdf'
  },
  {
    title: 'Excel to PDF',
    description: 'Convert Excel spreadsheets (XLSX) to PDF format',
    icon: FileText,
    color: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
    action: 'excel-to-pdf'
  },
  {
    title: 'HTML to PDF',
    description: 'Convert HTML files and web content to PDF documents',
    icon: FileText,
    color: 'bg-gradient-to-br from-amber-500 to-amber-600',
    action: 'html-to-pdf'
  },
  {
    title: 'PDF to Images',
    description: 'Convert PDF pages to JPG or PNG image files',
    icon: Image,
    color: 'bg-gradient-to-br from-violet-500 to-violet-600',
    action: 'pdf-to-images'
  },
  {
    title: 'PDF to Word',
    description: 'Convert PDF documents to editable Word (DOCX) files',
    icon: FileText,
    color: 'bg-gradient-to-br from-teal-500 to-teal-600',
    action: 'pdf-to-word'
  },
  {
    title: 'PDF to PowerPoint',
    description: 'Convert PDF to PowerPoint (PPTX) presentation format',
    icon: FileText,
    color: 'bg-gradient-to-br from-pink-500 to-pink-600',
    action: 'pdf-to-powerpoint'
  },
  {
    title: 'PDF to Excel',
    description: 'Convert PDF tables and data to Excel (XLSX) spreadsheets',
    icon: FileText,
    color: 'bg-gradient-to-br from-lime-500 to-lime-600',
    action: 'pdf-to-excel'
  },
  {
    title: 'PDF to PDF/A',
    description: 'Convert regular PDFs to PDF/A format for long-term archiving',
    icon: Archive,
    color: 'bg-gradient-to-br from-slate-500 to-slate-600',
    action: 'pdf-to-pdfa'
  },
  {
    title: 'Protect PDFs',
    description: 'Add password protection and encryption to secure your files',
    icon: Shield,
    color: 'bg-gradient-to-br from-red-500 to-red-600',
    action: 'protect'
  },
  {
    title: 'Extract Text',
    description: 'Pull text content from PDF files for editing and analysis',
    icon: FileSearch,
    color: 'bg-gradient-to-br from-indigo-500 to-indigo-600',
    action: 'extract'
  },
];

const features = [
  {
    icon: Zap,
    title: 'Lightning Fast',
    description: 'Process your PDFs in seconds with our optimized algorithms'
  },
  {
    icon: Shield,
    title: 'Secure & Private',
    description: 'Your files are processed securely and deleted after 24 hours'
  },
  {
    icon: Users,
    title: 'No Limits',
    description: 'Free to use with no file size or usage restrictions'
  }
];

export default function Home() {
  console.log('Home page rendered');

  return (
    <>
      {/* SEO Structured Data */}
      <HomePageStructuredData />
      <OrganizationStructuredData />

      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-hero text-white">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <Badge className="mb-4 bg-white/20 text-white border-white/30">
                ✨ Now with AI-powered optimization
              </Badge>

              <h1 className="text-4xl sm:text-6xl font-bold mb-6">
                Professional PDF Tools
                <span className="block text-blue-300">Made Simple</span>
              </h1>

              <p className="text-xl sm:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
                Merge, split, compress, and convert your PDF files with our powerful online tools.
                No software installation required.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link href="/tools/merge">
                  <Button
                    size="lg"
                    className="bg-white text-navy-900 hover:bg-gray-100 text-lg px-8 py-3"
                  >
                    Start Processing
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>

                <Link href="/blog">
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white text-white hover:bg-white hover:text-navy-900 text-lg px-8 py-3"
                  >
                    Read Our Blog
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>

          {/* Floating elements */}
          <motion.div
            animate={{ y: [0, -10, 0] }}
            transition={{ duration: 3, repeat: Infinity }}
            className="absolute top-20 left-10 w-16 h-16 bg-white/10 rounded-full"
          />
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 4, repeat: Infinity }}
            className="absolute bottom-20 right-10 w-12 h-12 bg-blue-300/20 rounded-full"
          />
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-navy-900 dark:text-white mb-4">
                Why Choose Our PDF Tools?
              </h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Built for professionals who need reliable, fast, and secure PDF processing.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <Card className="text-center h-full border-2 hover:border-blue-200 transition-colors">
                      <CardHeader>
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <CardTitle className="text-xl text-navy-900 dark:text-white">
                          {feature.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="text-gray-600 dark:text-gray-300">
                          {feature.description}
                        </CardDescription>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* PDF Tools Grid */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl sm:text-4xl font-bold text-navy-900 dark:text-white mb-4">
                Choose Your PDF Tool
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
                Select from our comprehensive suite of PDF processing tools.
                Each tool is optimized for speed and accuracy.
              </p>
            </motion.div>

            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {pdfTools.map((tool, index) => (
                <motion.div
                  key={tool.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <PdfToolCard
                    title={tool.title}
                    description={tool.description}
                    icon={tool.icon}
                    color={tool.color}
                    href={`/tools/${tool.action}`}
                  />
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-navy-900 text-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl sm:text-4xl font-bold mb-6">
                Ready to Start Processing?
              </h2>
              <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Join thousands of professionals who trust our PDF tools for their daily workflow.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/tools/merge">
                  <Button
                    size="lg"
                    className="bg-blue-500 hover:bg-blue-600 text-white text-lg px-8 py-3"
                  >
                    Get Started Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>

                <Link href="/blog">
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white text-white hover:bg-white hover:text-navy-900 text-lg px-8 py-3"
                  >
                    Learn More
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
}
