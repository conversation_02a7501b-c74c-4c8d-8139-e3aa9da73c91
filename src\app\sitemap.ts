import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://pdftools.com';
  
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/tools`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
  ];

  // PDF Tools pages
  const pdfTools = [
    'merge',
    'split',
    'compress',
    'images-to-pdf',
    'word-to-pdf',
    'powerpoint-to-pdf',
    'excel-to-pdf',
    'html-to-pdf',
    'pdf-to-images',
    'pdf-to-word',
    'pdf-to-powerpoint',
    'pdf-to-excel',
    'pdf-to-pdfa',
    'protect',
    'extract',
  ];

  const toolPages = pdfTools.map((tool) => ({
    url: `${baseUrl}/tools/${tool}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));

  // Blog posts (you can fetch these from your database)
  const blogPosts = [
    'merge-pdf-files-efficiently',
    'pdf-compression-best-practices',
    'converting-images-to-pdf-guide',
    'splitting-pdfs-extract-pages',
    'pdf-security-protection-guide',
  ];

  const blogPages = blogPosts.map((slug) => ({
    url: `${baseUrl}/blog/${slug}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.6,
  }));

  return [...staticPages, ...toolPages, ...blogPages];
}
