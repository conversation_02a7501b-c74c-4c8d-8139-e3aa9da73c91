'use client';

import { useState } from 'react';
import { use<PERSON>ara<PERSON> } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  FileText,
  Scissors,
  Archive,
  Image,
  Shield,
  FileSearch,
  Download,
  ArrowLeft,
  Loader2
} from 'lucide-react';
import { But<PERSON> } from '@/src/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card';
import FileUploadZone from '@/src/components/pdf/FileUploadZone';
import Link from 'next/link';
import { toast } from 'sonner';

const toolConfigs = {
  merge: {
    title: 'Merge PDFs',
    description: 'Combine multiple PDF files into a single document',
    icon: FileText,
    color: 'from-blue-500 to-blue-600',
    accept: '.pdf',
    multiple: true,
    instructions: 'Upload 2 or more PDF files to merge them into one document. Files will be combined in the order you upload them.'
  },
  split: {
    title: 'Split PDF',
    description: 'Extract specific pages or split documents into separate files',
    icon: Scissors,
    color: 'from-green-500 to-green-600',
    accept: '.pdf',
    multiple: false,
    instructions: 'Upload a PDF file to split it into individual pages or extract specific page ranges.'
  },
  compress: {
    title: 'Compress PDF',
    description: 'Reduce file size while maintaining quality',
    icon: Archive,
    color: 'from-purple-500 to-purple-600',
    accept: '.pdf',
    multiple: false,
    instructions: 'Upload a PDF file to compress it and reduce its file size for easier sharing.'
  },
  'images-to-pdf': {
    title: 'Images to PDF',
    description: 'Convert JPG, PNG and other images into PDF documents',
    icon: Image,
    color: 'from-orange-500 to-orange-600',
    accept: '.jpg,.jpeg,.png,.gif,.bmp,.tiff',
    multiple: true,
    instructions: 'Upload one or more image files to convert them into a PDF document.'
  },
  'word-to-pdf': {
    title: 'Word to PDF',
    description: 'Convert Word documents (DOCX) to PDF format',
    icon: FileText,
    color: 'from-cyan-500 to-cyan-600',
    accept: '.doc,.docx',
    multiple: true,
    instructions: 'Upload Word documents (.docx, .doc) to convert them to PDF format.'
  },
  'powerpoint-to-pdf': {
    title: 'PowerPoint to PDF',
    description: 'Convert PowerPoint presentations (PPTX) to PDF format',
    icon: FileText,
    color: 'from-rose-500 to-rose-600',
    accept: '.ppt,.pptx',
    multiple: true,
    instructions: 'Upload PowerPoint presentations (.pptx, .ppt) to convert them to PDF format.'
  },
  'excel-to-pdf': {
    title: 'Excel to PDF',
    description: 'Convert Excel spreadsheets (XLSX) to PDF format',
    icon: FileText,
    color: 'from-emerald-500 to-emerald-600',
    accept: '.xls,.xlsx',
    multiple: true,
    instructions: 'Upload Excel spreadsheets (.xlsx, .xls) to convert them to PDF format.'
  },
  'html-to-pdf': {
    title: 'HTML to PDF',
    description: 'Convert HTML files and web content to PDF documents',
    icon: FileText,
    color: 'from-amber-500 to-amber-600',
    accept: '.html,.htm',
    multiple: true,
    instructions: 'Upload HTML files to convert them into PDF documents.'
  },
  'pdf-to-images': {
    title: 'PDF to Images',
    description: 'Convert PDF pages to JPG or PNG image files',
    icon: Image,
    color: 'from-violet-500 to-violet-600',
    accept: '.pdf',
    multiple: false,
    instructions: 'Upload a PDF file to convert each page into separate image files (JPG format).'
  },
  'pdf-to-word': {
    title: 'PDF to Word',
    description: 'Convert PDF documents to editable Word (DOCX) files',
    icon: FileText,
    color: 'from-teal-500 to-teal-600',
    accept: '.pdf',
    multiple: false,
    instructions: 'Upload a PDF file to convert it to an editable Word document (.docx).'
  },
  'pdf-to-powerpoint': {
    title: 'PDF to PowerPoint',
    description: 'Convert PDF to PowerPoint (PPTX) presentation format',
    icon: FileText,
    color: 'from-pink-500 to-pink-600',
    accept: '.pdf',
    multiple: false,
    instructions: 'Upload a PDF file to convert it to a PowerPoint presentation (.pptx).'
  },
  'pdf-to-excel': {
    title: 'PDF to Excel',
    description: 'Convert PDF tables and data to Excel (XLSX) spreadsheets',
    icon: FileText,
    color: 'from-lime-500 to-lime-600',
    accept: '.pdf',
    multiple: false,
    instructions: 'Upload a PDF file containing tables or data to convert it to an Excel spreadsheet (.xlsx).'
  },
  'pdf-to-pdfa': {
    title: 'PDF to PDF/A',
    description: 'Convert regular PDFs to PDF/A format for long-term archiving',
    icon: Archive,
    color: 'from-slate-500 to-slate-600',
    accept: '.pdf',
    multiple: true,
    instructions: 'Upload PDF files to convert them to PDF/A format for long-term digital preservation.'
  },
  protect: {
    title: 'Protect PDF',
    description: 'Add password protection and encryption',
    icon: Shield,
    color: 'from-red-500 to-red-600',
    accept: '.pdf',
    multiple: false,
    instructions: 'Upload a PDF file to add password protection and encryption for security.'
  },
  extract: {
    title: 'Extract Text',
    description: 'Pull text content from PDF files',
    icon: FileSearch,
    color: 'from-indigo-500 to-indigo-600',
    accept: '.pdf',
    multiple: false,
    instructions: 'Upload a PDF file to extract all text content for editing and analysis.'
  }
};

export default function PdfToolPage() {
  const params = useParams();
  const toolId = params.tool as string;
  const config = toolConfigs[toolId as keyof typeof toolConfigs];

  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [processing, setProcessing] = useState(false);
  const [result, setResult] = useState<string | null>(null);

  console.log('PDF tool page rendered:', toolId, 'files:', selectedFiles.length);

  if (!config) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-navy-900 dark:text-white mb-4">
            Tool Not Found
          </h1>
          <Link href="/">
            <Button>Back to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  const Icon = config.icon;

  const handleFilesSelected = (files: File[]) => {
    console.log('Files selected for', toolId, ':', files.length);
    setSelectedFiles(files);
  };

  const handleProcess = async () => {
    if (selectedFiles.length === 0) {
      toast.error('Please select files to process');
      return;
    }

    console.log('Starting processing for', toolId);
    setProcessing(true);

    try {
      const formData = new FormData();
      selectedFiles.forEach(file => {
        formData.append('files', file);
      });
      formData.append('operation', toolId);

      const response = await fetch('/api/pdf/process', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Processing failed');
      }

      const result = await response.blob();
      const url = URL.createObjectURL(result);
      setResult(url);

      toast.success('Processing completed successfully!');
      console.log('Processing completed for', toolId);

    } catch (error) {
      console.error('Processing error:', error);
      toast.error('Failed to process files. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const handleDownload = () => {
    if (result) {
      console.log('Downloading result');
      const a = document.createElement('a');
      a.href = result;
      const extension = toolId.includes('pdf-to-images') ? '.zip' :
        toolId.includes('pdf-to-word') ? '.docx' :
          toolId.includes('pdf-to-powerpoint') ? '.pptx' :
            toolId.includes('pdf-to-excel') ? '.xlsx' : '.pdf';
      a.download = `${toolId}-result${extension}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Tools
          </Link>

          <div className={`w-20 h-20 bg-gradient-to-br ${config.color} rounded-full mx-auto mb-6 flex items-center justify-center shadow-lg`}>
            <Icon className="h-10 w-10 text-white" />
          </div>

          <h1 className="text-3xl sm:text-4xl font-bold text-navy-900 dark:text-white mb-4">
            {config.title}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {config.description}
          </p>
        </motion.div>

        {/* Tool Interface */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Upload Files</CardTitle>
              <CardDescription>{config.instructions}</CardDescription>
            </CardHeader>
            <CardContent>
              <FileUploadZone
                onFilesSelected={handleFilesSelected}
                accept={config.accept}
                multiple={config.multiple}
                maxSize={50} // 50MB limit
              />
            </CardContent>
          </Card>

          {/* Processing Controls */}
          {selectedFiles.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="text-center mb-8"
            >
              <Button
                onClick={handleProcess}
                disabled={processing}
                size="lg"
                className={`bg-gradient-to-r ${config.color} text-white px-8 py-3`}
              >
                {processing ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Processing...
                  </>
                ) : (
                  `Process ${selectedFiles.length} File${selectedFiles.length > 1 ? 's' : ''}`
                )}
              </Button>
            </motion.div>
          )}

          {/* Results */}
          {result && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4 }}
            >
              <Card className="bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800">
                <CardHeader className="text-center">
                  <CardTitle className="text-green-800 dark:text-green-200">
                    Processing Complete!
                  </CardTitle>
                  <CardDescription className="text-green-600 dark:text-green-300">
                    Your file has been processed successfully and is ready for download.
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button
                    onClick={handleDownload}
                    size="lg"
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <Download className="mr-2 h-5 w-5" />
                    Download Result
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
}