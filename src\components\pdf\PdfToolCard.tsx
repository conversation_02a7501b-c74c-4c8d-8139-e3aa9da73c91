'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Button } from '@/src/components/ui/button';
import { LucideIcon } from 'lucide-react';

interface PdfToolCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  color: string;
  href: string;
  disabled?: boolean;
}

export default function PdfToolCard({
  title,
  description,
  icon: Icon,
  color,
  href,
  disabled = false
}: PdfToolCardProps) {
  console.log('Rendering PDF tool card:', title);

  if (disabled) {
    return (
      <motion.div
        whileHover={{ y: -5, scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="h-full shadow-card transition-all duration-300 border-2 opacity-60">
          <CardHeader className="text-center pb-2">
            <div className={`w-16 h-16 rounded-full ${color} mx-auto mb-4 flex items-center justify-center shadow-lg opacity-50`}>
              <Icon className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-xl font-semibold text-navy-900 dark:text-white">
              {title}
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
              {description}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-2">
            <Button
              disabled={true}
              className="w-full bg-gradient-to-r from-gray-400 to-gray-500 text-white font-medium py-2 rounded-lg cursor-not-allowed"
            >
              Coming Soon
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <Link href={href} className="block h-full">
      <motion.div
        whileHover={{ y: -5, scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.2 }}
        className="h-full"
      >
        <Card className="h-full cursor-pointer shadow-card hover:shadow-card-hover transition-all duration-300 border-2 hover:border-blue-200">
          <CardHeader className="text-center pb-2">
            <div className={`w-16 h-16 rounded-full ${color} mx-auto mb-4 flex items-center justify-center shadow-lg`}>
              <Icon className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-xl font-semibold text-navy-900 dark:text-white">
              {title}
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
              {description}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-2">
            <Button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-2 rounded-lg transition-all duration-200">
              Get Started
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </Link>
  );
}