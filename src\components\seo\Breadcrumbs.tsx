'use client';

import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import StructuredData from './StructuredData';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
}

export default function Breadcrumbs({ items, className = '' }: BreadcrumbsProps) {
  // Create structured data for breadcrumbs
  const breadcrumbStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: 'https://pdftools.com',
      },
      ...items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 2,
        name: item.label,
        ...(item.href && { item: `https://pdftools.com${item.href}` }),
      })),
    ],
  };

  return (
    <>
      {/* Structured Data for Breadcrumbs */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbStructuredData) }}
      />
      
      {/* Visual Breadcrumbs */}
      <nav className={`flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 ${className}`} aria-label="Breadcrumb">
        <Link 
          href="/" 
          className="flex items-center hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          aria-label="Go to homepage"
        >
          <Home className="w-4 h-4" />
          <span className="sr-only">Home</span>
        </Link>
        
        {items.map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            <ChevronRight className="w-4 h-4 text-gray-400" />
            {item.href ? (
              <Link 
                href={item.href} 
                className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                {item.label}
              </Link>
            ) : (
              <span className="text-gray-900 dark:text-gray-100 font-medium">
                {item.label}
              </span>
            )}
          </div>
        ))}
      </nav>
    </>
  );
}

// Predefined breadcrumbs for common pages
export const ToolsBreadcrumb = ({ toolName }: { toolName: string }) => (
  <Breadcrumbs
    items={[
      { label: 'Tools', href: '/tools' },
      { label: toolName },
    ]}
  />
);

export const BlogBreadcrumb = ({ postTitle }: { postTitle?: string }) => (
  <Breadcrumbs
    items={[
      { label: 'Blog', href: '/blog' },
      ...(postTitle ? [{ label: postTitle }] : []),
    ]}
  />
);

export const AdminBreadcrumb = ({ section, subsection }: { section?: string; subsection?: string }) => (
  <Breadcrumbs
    items={[
      { label: 'Admin', href: '/admin' },
      ...(section ? [{ label: section, href: `/admin/${section.toLowerCase()}` }] : []),
      ...(subsection ? [{ label: subsection }] : []),
    ]}
  />
);
