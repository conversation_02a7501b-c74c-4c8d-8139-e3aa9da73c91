'use client';

interface StructuredDataProps {
  type: 'WebSite' | 'WebPage' | 'SoftwareApplication' | 'Article' | 'Organization';
  data: any;
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': type,
    ...data,
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// Predefined structured data for common pages
export const HomePageStructuredData = () => (
  <StructuredData
    type="WebSite"
    data={{
      name: 'PDFTools - Professional PDF Processing & Blog Platform',
      description: 'Comprehensive PDF tools for merging, splitting, compression, and conversion. Plus a full-featured blog platform with admin dashboard.',
      url: 'https://pdftools.com',
      potentialAction: {
        '@type': 'SearchAction',
        target: 'https://pdftools.com/search?q={search_term_string}',
        'query-input': 'required name=search_term_string',
      },
      mainEntity: {
        '@type': 'SoftwareApplication',
        name: 'PDFTools',
        applicationCategory: 'BusinessApplication',
        operatingSystem: 'Web Browser',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD',
        },
        featureList: [
          'PDF Merge',
          'PDF Split',
          'PDF Compression',
          'PDF to Image Conversion',
          'Document Format Conversion',
          'PDF Protection',
          'Text Extraction',
        ],
      },
    }}
  />
);

export const OrganizationStructuredData = () => (
  <StructuredData
    type="Organization"
    data={{
      name: 'PDFTools',
      description: 'Professional PDF processing tools and blog platform',
      url: 'https://pdftools.com',
      logo: 'https://pdftools.com/logo.png',
      sameAs: [
        'https://twitter.com/pdftools',
        'https://linkedin.com/company/pdftools',
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        contactType: 'customer service',
        email: '<EMAIL>',
      },
    }}
  />
);

export const PDFToolStructuredData = ({ toolName, description, url }: { toolName: string; description: string; url: string }) => (
  <StructuredData
    type="SoftwareApplication"
    data={{
      name: `${toolName} - PDFTools`,
      description,
      url,
      applicationCategory: 'BusinessApplication',
      operatingSystem: 'Web Browser',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
      },
      publisher: {
        '@type': 'Organization',
        name: 'PDFTools',
      },
    }}
  />
);
