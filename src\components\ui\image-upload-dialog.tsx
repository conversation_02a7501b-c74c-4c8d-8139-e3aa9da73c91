'use client';

import { useState } from 'react';
import { Button } from '@/src/components/ui/button';
import { Input } from '@/src/components/ui/input';
import { Label } from '@/src/components/ui/label';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/src/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/src/components/ui/dialog';
import ImageUpload from '@/src/components/ui/image-upload';

interface ImageUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImageSelect: (url: string, alt?: string) => void;
}

export default function ImageUploadDialog({
  open,
  onOpenChange,
  onImageSelect,
}: ImageUploadDialogProps) {
  const [imageUrl, setImageUrl] = useState('');
  const [altText, setAltText] = useState('');
  const [uploadedUrl, setUploadedUrl] = useState('');

  const handleUrlSubmit = () => {
    if (imageUrl.trim()) {
      onImageSelect(imageUrl.trim(), altText.trim() || undefined);
      handleClose();
    }
  };

  const handleUploadedImage = (url: string) => {
    setUploadedUrl(url);
  };

  const handleUploadSubmit = () => {
    if (uploadedUrl) {
      onImageSelect(uploadedUrl, altText.trim() || undefined);
      handleClose();
    }
  };

  const handleClose = () => {
    setImageUrl('');
    setAltText('');
    setUploadedUrl('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Insert Image</DialogTitle>
          <DialogDescription>
            Upload an image or provide a URL to insert into your post.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload Image</TabsTrigger>
            <TabsTrigger value="url">Image URL</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            <div>
              <Label>Upload Image</Label>
              <div className="mt-2">
                {uploadedUrl ? (
                  <div className="space-y-3">
                    <img
                      src={uploadedUrl}
                      alt="Uploaded preview"
                      className="w-full h-48 object-cover rounded-lg border"
                    />
                    <Button
                      variant="outline"
                      onClick={() => setUploadedUrl('')}
                      className="w-full"
                    >
                      Upload Different Image
                    </Button>
                  </div>
                ) : (
                  <ImageUpload
                    onImageUploaded={handleUploadedImage}
                    className="w-full"
                  />
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="upload-alt">Alt Text (Optional)</Label>
              <Input
                id="upload-alt"
                placeholder="Describe the image for accessibility..."
                value={altText}
                onChange={(e) => setAltText(e.target.value)}
              />
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button onClick={handleUploadSubmit} disabled={!uploadedUrl}>
                Insert Image
              </Button>
            </DialogFooter>
          </TabsContent>

          <TabsContent value="url" className="space-y-4">
            <div>
              <Label htmlFor="image-url">Image URL</Label>
              <Input
                id="image-url"
                placeholder="https://example.com/image.jpg"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="url-alt">Alt Text (Optional)</Label>
              <Input
                id="url-alt"
                placeholder="Describe the image for accessibility..."
                value={altText}
                onChange={(e) => setAltText(e.target.value)}
              />
            </div>

            {imageUrl && (
              <div>
                <Label>Preview</Label>
                <img
                  src={imageUrl}
                  alt="Preview"
                  className="w-full h-48 object-cover rounded-lg border mt-2"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button onClick={handleUrlSubmit} disabled={!imageUrl.trim()}>
                Insert Image
              </Button>
            </DialogFooter>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
