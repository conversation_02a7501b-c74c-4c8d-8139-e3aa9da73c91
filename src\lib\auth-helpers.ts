import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { NextRequest } from 'next/server';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  name: string;
}

export function hashPassword(password: string): Promise<string> {
  console.log('Hashing password...');
  return bcrypt.hash(password, 12);
}

export function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  console.log('Verifying password...');
  return bcrypt.compare(password, hashedPassword);
}

export function signJWT(payload: JWTPayload): string {
  console.log('Signing JWT for user:', payload.userId);
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN } as jwt.SignOptions);
}

export function verifyJWT(token: string): JWTPayload | null {
  try {
    console.log('Verifying JWT token...');
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    return decoded;
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
}

export function getTokenFromRequest(request: NextRequest): string | null {
  // Try Authorization header first
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Fallback to cookie
  const token = request.cookies.get('auth-token')?.value;
  return token || null;
}

export function requireAuth(request: NextRequest): JWTPayload | null {
  const token = getTokenFromRequest(request);
  if (!token) {
    console.log('No auth token found');
    return null;
  }
  
  const payload = verifyJWT(token);
  if (!payload) {
    console.log('Invalid auth token');
    return null;
  }
  
  console.log('Auth check passed for user:', payload.userId);
  return payload;
}

export function requireRole(request: NextRequest, allowedRoles: string[]): JWTPayload | null {
  const user = requireAuth(request);
  if (!user) {
    return null;
  }
  
  if (!allowedRoles.includes(user.role)) {
    console.log(`Access denied. User role: ${user.role}, Required: ${allowedRoles.join(', ')}`);
    return null;
  }
  
  return user;
}