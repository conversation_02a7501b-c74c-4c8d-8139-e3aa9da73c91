import { NextAuthConfig } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import dbConnect from '@/src/lib/database';
import User from '@/src/lib/models/User';

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export const authConfig: NextAuthConfig = {
  pages: {
    signIn: '/auth/login',
    signOut: '/auth/login',
  },
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnAdmin = nextUrl.pathname.startsWith('/admin');

      if (isOnAdmin) {
        if (isLoggedIn && auth.user.role === 'admin') return true;
        return false; // Redirect unauthenticated users to login page
      }

      return true;
    },
    jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.id = user.id;
      }
      return token;
    },
    session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
      }
      return session;
    },
  },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        console.log('NextAuth: Authorizing user...');

        try {
          const { email, password } = loginSchema.parse(credentials);

          const dbConnection = await dbConnect();

          if (!dbConnection) {
            console.log('NextAuth: Database connection failed');
            return null;
          }

          // Find user in database
          const user = await User.findOne({ email, isActive: true });
          console.log('NextAuth: Database user found:', !!user);

          if (!user) {
            console.log('NextAuth: User not found for email:', email);
            return null;
          }

          // Verify password
          const isValid = await bcrypt.compare(password, user.password);
          if (!isValid) {
            console.log('NextAuth: Invalid password for user:', email);
            return null;
          }

          console.log('NextAuth: User authenticated successfully:', email);

          return {
            id: user.id || user._id?.toString(),
            email: user.email,
            name: user.name,
            role: user.role,
          };
        } catch (error) {
          console.error('NextAuth: Authorization error:', error);
          return null;
        }
      },
    }),
  ],
} satisfies NextAuthConfig;