import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/pdftools';

interface GlobalThis {
  mongoose?: {
    conn: mongoose.Connection | null;
    promise: Promise<mongoose.Connection> | null;
  };
}

declare const globalThis: GlobalThis;

let cached = globalThis.mongoose;

if (!cached) {
  cached = globalThis.mongoose = { conn: null, promise: null };
}

async function dbConnect(): Promise<mongoose.Connection | null> {
  console.log('Attempting database connection...');
  
  // In development/demo environments, gracefully handle missing MongoDB
  if (!process.env.MONGODB_URI && process.env.NODE_ENV !== 'production') {
    console.log('No MongoDB URI provided, running in demo mode without database');
    return null;
  }
  
  if (cached!.conn) {
    console.log('Using cached database connection');
    return cached!.conn;
  }

  if (!cached!.promise) {
    const opts = {
      bufferCommands: false,
      serverSelectionTimeoutMS: 5000, // 5 second timeout
      socketTimeoutMS: 45000,
    };

    cached!.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      console.log('New database connection established');
      return mongoose.connection;
    }).catch((error) => {
      console.error('Database connection failed:', error.message);
      cached!.promise = null; // Reset promise on failure
      return null;
    });
  }

  try {
    cached!.conn = await cached!.promise;
    return cached!.conn;
  } catch (e) {
    cached!.promise = null;
    console.error('Database connection error:', e);
    
    // Return null instead of throwing in demo environments
    if (process.env.NODE_ENV !== 'production') {
      console.log('Continuing without database connection in demo mode');
      return null;
    }
    
    throw e;
  }
}

export default dbConnect;