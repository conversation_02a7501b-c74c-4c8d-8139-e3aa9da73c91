import mongoose, { Document, Schema } from 'mongoose';

export interface IPdfOperation extends Document {
  _id: string;
  user?: mongoose.Types.ObjectId;
  operation: 'merge' | 'split' | 'compress' | 'convert' | 'protect' | 'extract';
  originalFiles: string[];
  outputFile?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  fileSize: number;
  processingTime?: number;
  ipAddress?: string;
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
}

const PdfOperationSchema = new Schema<IPdfOperation>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  operation: {
    type: String,
    required: true,
    enum: ['merge', 'split', 'compress', 'convert', 'protect', 'extract'],
  },
  originalFiles: [{
    type: String,
    required: true,
  }],
  outputFile: {
    type: String,
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending',
  },
  fileSize: {
    type: Number,
    required: true,
    min: 0,
  },
  processingTime: {
    type: Number,
    min: 0,
  },
  ipAddress: {
    type: String,
  },
  errorMessage: {
    type: String,
  },
}, {
  timestamps: true,
});

// Indexes
PdfOperationSchema.index({ user: 1, createdAt: -1 });
PdfOperationSchema.index({ operation: 1 });
PdfOperationSchema.index({ status: 1 });
PdfOperationSchema.index({ createdAt: -1 });

export default mongoose.models.PdfOperation || mongoose.model<IPdfOperation>('PdfOperation', PdfOperationSchema);