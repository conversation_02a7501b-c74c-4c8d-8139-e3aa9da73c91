const BASE_URL = 'http://localhost:3001';

async function testWordToPdf() {
  console.log('🧪 Testing Word to PDF conversion...\n');

  // Create a simple DOCX-like content for testing
  const docxContent = `Test Word Document
This is a test document with real content that should be preserved in the PDF conversion.

Key points to test:
1. Text preservation
2. Formatting retention
3. No demo placeholders
4. Actual document processing

This content should appear in the final PDF without any "demo" or "placeholder" text.`;

  const formData = new FormData();
  const blob = new Blob([docxContent], { 
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
  });
  formData.append('files', blob, 'test-document.docx');
  formData.append('operation', 'word-to-pdf');

  try {
    console.log('📤 Sending conversion request...');
    const response = await fetch(`${BASE_URL}/api/pdf/process`, {
      method: 'POST',
      body: formData,
    });

    console.log(`📥 Response: ${response.status} ${response.statusText}`);
    console.log(`📄 Content-Type: ${response.headers.get('content-type')}`);
    console.log(`📏 Content-Length: ${response.headers.get('content-length')}`);

    if (response.ok) {
      const pdfBuffer = await response.arrayBuffer();
      console.log(`✅ Word to PDF conversion successful!`);
      console.log(`📊 Generated PDF size: ${pdfBuffer.byteLength} bytes`);
      
      // Check if it's a valid PDF
      const pdfString = new TextDecoder('latin1').decode(pdfBuffer.slice(0, 100));
      if (pdfString.startsWith('%PDF')) {
        console.log('✅ Valid PDF format detected');
      } else {
        console.log('⚠️  Invalid PDF format');
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ Conversion failed:', errorText);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

async function testImagesToPdf() {
  console.log('\n🧪 Testing Images to PDF conversion...\n');

  // Create a simple test image (1x1 pixel PNG)
  const pngData = new Uint8Array([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
    0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
    0x01, 0x00, 0x01, 0x02, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E,
    0x44, 0xAE, 0x42, 0x60, 0x82
  ]);

  const formData = new FormData();
  const blob = new Blob([pngData], { type: 'image/png' });
  formData.append('files', blob, 'test-image.png');
  formData.append('operation', 'images-to-pdf');

  try {
    console.log('📤 Sending conversion request...');
    const response = await fetch(`${BASE_URL}/api/pdf/process`, {
      method: 'POST',
      body: formData,
    });

    console.log(`📥 Response: ${response.status} ${response.statusText}`);
    console.log(`📄 Content-Type: ${response.headers.get('content-type')}`);

    if (response.ok) {
      const pdfBuffer = await response.arrayBuffer();
      console.log(`✅ Images to PDF conversion successful!`);
      console.log(`📊 Generated PDF size: ${pdfBuffer.byteLength} bytes`);
      
      // Check if it's a valid PDF
      const pdfString = new TextDecoder('latin1').decode(pdfBuffer.slice(0, 100));
      if (pdfString.startsWith('%PDF')) {
        console.log('✅ Valid PDF format detected');
      } else {
        console.log('⚠️  Invalid PDF format');
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ Conversion failed:', errorText);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Starting PDF API Tests\n');
  console.log('=' .repeat(50));
  
  await testWordToPdf();
  await testImagesToPdf();
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 Tests completed!');
}

// Run the tests
runTests().catch(console.error);
