const JSZip = require('jszip');
const BASE_URL = 'http://localhost:3001';

async function createRealDocx() {
  const zip = new JSZip();
  
  // Create a proper DOCX structure
  const documentXml = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:body>
    <w:p>
      <w:r>
        <w:t>Lahcen Assmira</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Software Developer</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Email: <EMAIL></w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Phone: +****************</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t></w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Professional Summary</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Experienced software developer with expertise in web technologies, database design, and system architecture. Passionate about creating efficient and scalable solutions.</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t></w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Technical Skills</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>- JavaScript, TypeScript, React, Node.js</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>- Python, Django, Flask</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>- SQL, MongoDB, PostgreSQL</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>- AWS, Docker, Kubernetes</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t></w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Work Experience</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Senior Software Developer - TechCorp Inc (2020-Present)</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>- Led development of microservices architecture</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>- Improved system performance by 40%</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>- Mentored junior developers</w:t>
      </w:r>
    </w:p>
  </w:body>
</w:document>`;

  const contentTypes = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
  <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
  <Default Extension="xml" ContentType="application/xml"/>
  <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>`;

  const rels = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
  <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>`;

  zip.file('word/document.xml', documentXml);
  zip.file('[Content_Types].xml', contentTypes);
  zip.file('_rels/.rels', rels);

  return await zip.generateAsync({ type: 'uint8array' });
}

async function testWordToPdf() {
  console.log('🧪 Testing DOCX to PDF Conversion with Real DOCX File...\n');

  try {
    // Create a real DOCX file
    const docxBuffer = await createRealDocx();
    console.log(`✅ Created DOCX file (${docxBuffer.length} bytes)`);

    // Test the conversion
    const formData = new FormData();
    const blob = new Blob([docxBuffer], { 
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
    });
    formData.append('files', blob, 'Lahcen-Assmira_CV.docx');
    formData.append('operation', 'word-to-pdf');

    console.log('📤 Sending conversion request...');
    const response = await fetch(`${BASE_URL}/api/pdf/process`, {
      method: 'POST',
      body: formData,
    });

    console.log(`📥 Response: ${response.status} ${response.statusText}`);
    console.log(`📄 Content-Type: ${response.headers.get('content-type')}`);

    if (response.ok) {
      const pdfBuffer = await response.arrayBuffer();
      console.log(`✅ PDF generated successfully (${pdfBuffer.byteLength} bytes)`);
      
      // Check if the PDF contains actual content (basic check)
      const pdfString = Buffer.from(pdfBuffer).toString('latin1');
      
      const contentChecks = [
        'Lahcen Assmira',
        'Software Developer',
        'Professional Summary',
        'Technical Skills',
        'Work Experience',
        'TechCorp Inc',
        'JavaScript',
        'React'
      ];
      
      const foundContent = contentChecks.filter(content => 
        pdfString.includes(content)
      );
      
      console.log(`✅ Content verification: Found ${foundContent.length}/${contentChecks.length} expected items`);
      console.log(`📋 Found content: ${foundContent.join(', ')}`);
      
      if (foundContent.length > 0) {
        console.log('🎉 SUCCESS: PDF contains actual document content!');
      } else {
        console.log('⚠️  WARNING: PDF may not contain expected content');
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ PDF conversion failed:', errorText);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

// Run the test
testWordToPdf().catch(console.error);
